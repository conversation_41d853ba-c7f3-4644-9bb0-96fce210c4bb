"use client"
import { <PERSON>, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { ExternalLink, X } from "lucide-react"
import AnimatedSection from "./animated-section"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useState, useEffect } from "react"
import type { Experience } from "@/types/experience"
import { useIsSmallScreen } from "@/hooks/use-mobile"

// Experience data array - can be edited to add new experiences
const experiences: Experience[] = [
  {
    title: "Senior Software Engineer",
    company: "Lexia Learning",
    period: "July 2025 - Present",
    location: "Remote",
    challenge: 'Lexia is a 40-year-old language learning company focused on improving K-12 English literacy for students. Serving 1 in 3 public school districts, Lexia serves 7+ million students and 300k educators helping to address the national literacy crisis. I joined the myLexia team, which is the educator-facing product that teachers and school admins use to manage students, monitor progress, and manage their classrooms. I was brought in to help modernize the frontend migrating from AngularJs to Angular.',
    solution: 'This being my 2nd large codebase migration from AngularJs to a modern web dev framework, it was not my first rodeo. I joined as an individual contributor, but quickly have become an integral part of the team bringing my expertise in modern component-based frontend development, agile process improvement, and product thinking to the team.',
    impact: [],
    learnings: '',
    visuals: [],
    callToAction: {
      text: "Lexia Learning Website",
      url: "https://www.lexialearning.com"
    },
    type: "work"
  },
  {
    title: "Portfolio Website",
    company: "Side Project",
    period: "April 2025",
    location: "Personal Project",
    challenge: "Creating a personal portfolio website that stands out in a sea of templates while showcasing my skills and personality in a unique way.",
    solution: "This website! I designed this portfolio website with an Outrun, synthwave, and retro aesthetic. Built with Next.js, React, and Tailwind CSS featuring custom animations and responsive design. The initial mockup was done with v0.dev and then built out from there.",
    impact: [
      "Brushed up on modern Next.js best practice patterns and practices",
      "Created responsive design that works across all devices",
      "Optimized for a 100% Google Chrome Lighthouse score"
    ],
    learnings: "This project was a fun way to keep my front-end web development skills up-to-date when building a Next.js/React web app from scratch for this website. since I had been working on a mobile app for the previous year. optimized, and reinforced my skills in modern front-end development while allowing me to express my creativity through design. I learned valuable lessons about balancing aesthetics with performance and accessibility.",
    callToAction: {
      text: "View on GitHub",
      url: "https://github.com/seanblonien/portfolio"
    },
    type: "project"
  },
  {
    title: "Director of Engineering",
    company: "Fly Bodies",
    period: "May 2024 - July 2025",
    location: "Remote/Los Angeles, CA",
    challenge: "Fly Bodies is a health and wellness startup that helps schools and small organizations keep their communities active in a simple, motivating way. Our users are teachers, staff, and small teams who want to move more but struggle with consistency and accountability. Many do not have time for complex tools or the budget for enterprise wellness platforms. They need something that feels fun and social, works on the phones and wearables they already use, and respects privacy. The core problem to solve was making daily activity feel rewarding and shared through challenges, streaks, and personal goals. The platform needed to be easy to onboard, reliable across iOS and Android, and securely sync activity from Apple Health, Health Connect, and Fitbit in real time so people could see progress and cheer each other on without friction.",
    solution: "I joined as the 1st engineer and took the product from 0 to 1. I led discovery, planning, design, architecture, development, testing, and releases to ship the FlyFit mobile app and supporting backend. At a high level, I built a modern, cross-platform app using React Native and TypeScript, paired with a serverless Node.js backend on GCP and Firebase. The system delivers real time updates for leaderboards, feeds, and shared workouts so the app feels instant and never needs a manual refresh. I integrated Apple Health, Health Connect, and Fitbit for seamless activity sync, and set up a solid CI/CD pipeline with GitHub Actions and Expo EAS to support weekly releases and fast iteration based on user feedback.",
    impact: [
      "Product and delivery: Shipped the MVP in 3 months, sustained weekly releases, and earned a 5-star App Store rating.",
      "Growth and outcomes: Achieved 1,500+ organic downloads and $30K+ in first-year revenue with zero marketing spend.",
      "Architecture and scale: Designed a real time, serverless platform on GCP/Firebase that supported 300+ MAUs with autoscaling and a local-first, zero-refresh UX.",
      "Engagement: Implemented gamification features including streaks, quizzes, and goals that increased engagement by 210%.",
      "Cost efficiency: Reduced initial cloud costs by 40% through architecture and data model optimizations.",
      "Leadership and communication: Owned the full lifecycle from roadmap and technical design to integrations, security, testing, and App Store releases while aligning stakeholders and communicating progress clearly with nontechnical partners and customers.",
    ],
    learnings: "This role sharpened my product engineering skills. I translated user needs into a focused roadmap, kept scope tight, and iterated quickly based on what we heard from schools and teams using the app. In a true startup environment, I wore multiple hats and enjoyed it. I acted as an IC, architect, product partner, and delivery lead while building modern, scalable infrastructure that I designed end to end. It reinforced how to make pragmatic, cost-aware technical decisions, work on the cutting edge with real time systems and serverless tech, and lead early stage product development and technical strategy under tight budget constraints.",
    callToAction: {
      text: "View on Fly Bodies website",
      url: "https://www.fly-bodies.com/flyfit-app"
    },
    type: "work"
  },
  {
    title: "Senior Software Consultant",
    company: "Pariveda Solutions",
    period: "August 2022 - May 2024",
    location: "Dallas, TX",
    challenge: "Delivering high-quality technical solutions for enterprise clients with complex requirements and legacy systems while meeting tight deadlines and managing stakeholder expectations.",
    solution: "Led technical delivery for enterprise clients including UnitedHealthcare, Southwest Airlines, and Toyota Financial Services, focusing on front-end migrations, data pipelines, and cloud architecture.",
    impact: [
      "Led a team of 8 developers on the UnitedHealthcare project",
      "Reduced page load times by 60% through code splitting and optimization",
      "Implemented CI/CD pipelines that cut deployment time from days to minutes"
    ],
    learnings: "Working across multiple industries and technologies taught me to quickly adapt to new domains and technical challenges. I developed strong skills in client communication, technical leadership, and delivering value in complex enterprise environments.",
    callToAction: {
      text: "View on LinkedIn",
      url: "https://linkedin.com/in/seanblonien"
    },
    type: "work"
  },
]

export default function Experience() {
  const isSmallScreen = useIsSmallScreen();
  const [openPopoverIndex, setOpenPopoverIndex] = useState<number | null>(null);
  const [touchStartY, setTouchStartY] = useState<number | null>(null);

  // Handle touch events for swipe to close on mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    if (isSmallScreen) {
      setTouchStartY(e.touches[0].clientY);
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (isSmallScreen && touchStartY && e.changedTouches[0].clientY - touchStartY > 70) {
      // Swipe down detected, close the popover
      setOpenPopoverIndex(null);
    }
    setTouchStartY(null);
  };

  // Fix for Radix UI popover positioning on small screens
  useEffect(() => {
    if (isSmallScreen && openPopoverIndex !== null) {
      // Small delay to ensure the popover is rendered
      setTimeout(() => {
        // Try to find the popover wrapper using :has() selector (modern browsers)
        let popperWrapper = document.querySelector('[data-radix-popper-content-wrapper]:has([data-experience-popover="true"])');

        // Fallback for browsers that don't support :has()
        if (!popperWrapper) {
          // Find all popper wrappers
          const wrappers = document.querySelectorAll('[data-radix-popper-content-wrapper]');
          // Find the one that contains our experience popover
          wrappers.forEach(wrapper => {
            if (wrapper.querySelector('[data-experience-popover="true"]')) {
              popperWrapper = wrapper;
            }
          });
        }

        if (popperWrapper) {
          // Apply direct styles to override any transforms
          (popperWrapper as HTMLElement).style.transform = 'none';
          (popperWrapper as HTMLElement).style.top = '0';
          (popperWrapper as HTMLElement).style.left = '0';
          (popperWrapper as HTMLElement).style.width = '100%';
          (popperWrapper as HTMLElement).style.height = '100%';
          (popperWrapper as HTMLElement).style.position = 'fixed';

          // Also apply styles to the popover content directly
          const popoverContent = popperWrapper.querySelector('[data-experience-popover="true"]');
          if (popoverContent) {
            (popoverContent as HTMLElement).style.transform = 'none';
            (popoverContent as HTMLElement).style.maxWidth = '100vw';
            (popoverContent as HTMLElement).style.width = '100vw';
            (popoverContent as HTMLElement).style.maxHeight = '100vh';
            (popoverContent as HTMLElement).style.height = '100vh';
          }
        }
      }, 50); // Small delay to ensure DOM is updated
    }
  }, [isSmallScreen, openPopoverIndex]);

  return (
    <section id="experience" className="section-container">
      <AnimatedSection>
        <h2 className="section-title">EXPERIENCE</h2>

        {/* Legend */}
        <div className="flex justify-center gap-8 mb-8" role="legend" aria-label="Timeline categories">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full border-2 border-neon-blue shadow-neon-blue" aria-hidden="true"></div>
            <span className="text-base text-text-white-80">Work Experience</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full border-2 border-neon-pink shadow-neon-pink" aria-hidden="true"></div>
            <span className="text-base text-text-white-80">Projects</span>
          </div>
        </div>

        {/* Neon Timeline Container */}
        <div className="relative mt-12" role="region" aria-label="Experience Timeline">
          {/* Center neon line - positioned differently on mobile */}
          <div
            className="absolute md:left-1/2 left-[20px] top-0 bottom-0 w-[2px] bg-gradient-to-b from-neon-blue via-neon-pink to-neon-blue
                     shadow-neon-blue rounded-full z-10"
            aria-hidden="true"
          ></div>

          {/* Timeline entries */}
          <div className="relative z-10">
            {experiences.map((experience, index) => (
              <AnimatedSection
                key={`${experience.company}-${index}`}
                delay={0.2}
                className="mb-16 relative"
              >
                <article className="experience-entry">
                  {/* Timeline node - different color based on type, positioned differently on mobile */}
                  <div
                    className={`absolute md:left-1/2 left-[20px] top-6 w-4 h-4 rounded-full bg-dark-blue border-2
                               ${experience.type === 'work'
                                 ? 'border-neon-blue shadow-neon-blue'
                                 : 'border-neon-pink shadow-neon-pink'}
                               md:-translate-x-1/2 z-20`}
                    aria-hidden="true"
                  ></div>

                  {/* Content container - alternating sides of timeline on desktop, all on right for mobile */}
                  <div className="flex md:justify-end justify-end relative z-30">
                    <Popover
                      open={openPopoverIndex === index}
                      onOpenChange={(open) => {
                        setOpenPopoverIndex(open ? index : null);
                      }}
                    >
                      <PopoverTrigger asChild>
                        <button
                          type="button"
                          aria-haspopup="dialog"
                          aria-expanded={openPopoverIndex === index}
                          aria-controls={`experience-popover-${index}`}
                          aria-label={`View details about ${experience.title} at ${experience.company}`}
                          className={`w-full md:w-[calc(50%-2rem)] text-left ${index % 2 === 0 ? 'md:mr-[calc(50%+2rem)]' : 'md:ml-auto'} ml-auto`}
                          onClick={() => setOpenPopoverIndex(openPopoverIndex === index ? null : index)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              setOpenPopoverIndex(openPopoverIndex === index ? null : index);
                            }
                          }}
                        >
                          <Card className={`card ${experience.type === 'work'
                            ? 'border-neon-blue-30 hover:border-neon-blue-70 hover:shadow-neon-blue-lg'
                            : 'border-neon-pink-30 hover:border-neon-pink-70 hover:shadow-neon-pink-lg'}
                            transition-all duration-300 rounded-xl overflow-hidden relative`}>
                            <CardHeader>
                              <div className="flex flex-col gap-1">
                                <CardTitle className={`text-xl font-vt323 ${experience.type === 'work' ? 'neon-text-blue' : 'neon-text-pink'}`}>
                                  {experience.title}
                                </CardTitle>
                                <CardDescription className={`text-lg ${experience.type === 'work' ? 'text-neon-pink' : 'text-neon-blue'}`}>
                                  {experience.company}
                                </CardDescription>
                                <time dateTime={experience.period.replace(/\s/g, '')} className="text-base text-text-white-70 mt-1">
                                  {experience.period}
                                </time>
                              </div>
                            </CardHeader>
                            {/* Mobile tap indicator */}
                            <div
                              className={`absolute bottom-2 right-2 ${!isSmallScreen ? 'hidden' : ''} rounded-full w-5 h-5 flex items-center justify-center
                                ${experience.type === 'work'
                                  ? 'bg-neon-blue/20 text-neon-blue border border-neon-blue/50'
                                  : 'bg-neon-pink/20 text-neon-pink border border-neon-pink/50'}`}
                              aria-hidden="true"
                            >
                              <span className="text-xs">+</span>
                            </div>
                          </Card>
                        </button>
                      </PopoverTrigger>

                    <PopoverContent
                      id={`experience-popover-${index}`}
                      className={`${isSmallScreen
                        ? 'w-screen h-screen fixed inset-0 max-w-[100vw] max-h-[100vh]'
                        : 'w-[500px] max-h-[600px]'}
                        backdrop-blur-sm border ${experience.type === 'work'
                        ? 'border-neon-blue-50 shadow-neon-blue-lg'
                        : 'border-neon-pink-50 shadow-neon-pink-lg'}
                        text-white p-3 md:p-4 z-[9999] ${isSmallScreen ? '' : 'rounded-xl'} overflow-y-auto`}
                      style={{
                        backgroundColor: 'rgba(10, 10, 32, 0.95)',
                        ...(isSmallScreen ? {
                          position: 'fixed',
                          inset: 0,
                          width: '100vw',
                          height: '100vh',
                          maxWidth: '100vw',
                          maxHeight: '100vh',
                          overflow: 'hidden',
                          transform: 'none !important'
                        } : {})
                      }}
                      side={isSmallScreen ? "bottom" : "left"}
                      align={isSmallScreen ? "center" : "start"}
                      sideOffset={isSmallScreen ? 0 : 20}
                      alignOffset={isSmallScreen ? 0 : undefined}
                      onTouchStart={handleTouchStart}
                      onTouchEnd={handleTouchEnd}
                      data-experience-popover="true"
                    >
                      <article className={`${isSmallScreen ? 'h-full flex flex-col' : 'space-y-3'}`} aria-labelledby={`experience-title-${index}`}>
                        {/* Mobile header with close button */}
                        {isSmallScreen && (
                          <header className="sticky top-0 left-0 right-0 pb-2 mb-2 border-b border-white/10 bg-[rgba(10,10,32,0.98)] z-10 pr-[17px]">
                            <div className="flex justify-center w-full">
                              <div className="flex justify-between items-center w-full max-w-[540px] px-3 md:px-6">
                                <h3 className={`text-2xl font-vt323 ${experience.type === 'work' ? 'neon-text-blue' : 'neon-text-pink'} truncate`}>
                                  Details
                                </h3>
                                <button
                                  onClick={() => setOpenPopoverIndex(null)}
                                  className="p-2 rounded-full hover:bg-white/10 transition-colors flex-shrink-0"
                                  aria-label="Close details"
                                  aria-controls={`experience-popover-${index}`}
                                >
                                  <X size={20} className="text-white/70" />
                                </button>
                              </div>
                            </div>
                          </header>
                        )}

                        {/* Scrollable content area */}
                        <div className={isSmallScreen ? "flex-1 overflow-y-auto pb-6" : ""}>
                          <div className="flex justify-center w-full">
                            <div className="space-y-3 w-full max-w-[540px] px-3 md:px-6 pb-safe">
                            <header className={`flex justify-between items-start ${isSmallScreen ? '' : 'mb-3'}`}>
                              <div>
                                <h4 id={`experience-title-${index}`} className={`text-xl font-vt323 ${experience.type === 'work' ? 'neon-text-blue' : 'neon-text-pink'}`}>
                                  {experience.title}
                                </h4>
                                <p className={`text-base ${experience.type === 'work' ? 'text-neon-pink' : 'text-neon-blue'}`}>
                                  {experience.company} • {experience.location}
                                </p>
                              </div>
                              {/* Close button - only visible on desktop */}
                              {!isSmallScreen && (
                                <button
                                  onClick={() => setOpenPopoverIndex(null)}
                                  className="p-1 rounded-full hover:bg-white/10 transition-colors"
                                  aria-label="Close details"
                                  aria-controls={`experience-popover-${index}`}
                                >
                                  <X size={16} className="text-white/70" />
                                </button>
                              )}
                            </header>

                            {/* Challenge */}
                            <section aria-labelledby={`challenge-heading-${index}`}>
                              <h5 id={`challenge-heading-${index}`} className="text-lg uppercase text-text-white-60 mb-1">Challenge</h5>
                              <p className="text-base text-text-white-90 break-words">{experience.challenge}</p>
                            </section>

                            {/* Solution */}
                            <section aria-labelledby={`solution-heading-${index}`}>
                              <h5 id={`solution-heading-${index}`} className="text-lg uppercase text-text-white-60 mb-1">Solution</h5>
                              <p className="text-base text-text-white-90 break-words">{experience.solution}</p>
                            </section>

                            {/* Impact */}
                            {experience.impact && experience.impact.length > 0 && (
                              <section aria-labelledby={`impact-heading-${index}`}>
                                <h5 id={`impact-heading-${index}`} className="text-lg uppercase text-text-white-60 mb-1">Key Impact</h5>
                                <ul className="list-disc pl-4 text-text-white-80 text-base space-y-2">
                                  {experience.impact.map((item, i) => (
                                    <li key={i} className="break-words">{item}</li>
                                  ))}
                                </ul>
                              </section>
                            )}

                            {/* Learnings */}
                            <section aria-labelledby={`learnings-heading-${index}`}>
                              <h5 id={`learnings-heading-${index}`} className="text-lg uppercase text-text-white-60 mb-1">Learnings</h5>
                              <p className="text-base text-text-white-90 break-words">{experience.learnings}</p>
                            </section>

                            {/* Call to Action */}
                            <footer>
                              <a
                                href={experience.callToAction.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className={`inline-flex items-center text-base ${experience.type === 'work'
                                  ? 'text-neon-blue hover:text-neon-pink'
                                  : 'text-neon-pink hover:text-neon-blue'} transition-colors`}
                              >
                                {experience.callToAction.text} <ExternalLink size={16} className="ml-1" />
                              </a>
                            </footer>
                            </div>
                          </div>
                        </div>
                      </article>
                    </PopoverContent>
                  </Popover>
                </div>
                </article>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </AnimatedSection>
    </section>
  )
}
